// CHESS ENGINE SEARCH IMPLEMENTATION
// Implementasi search functions untuk ChessEngine

#include "ChessEngine.h"
#include <algorithm>
#include <cmath>

int ChessEngine::principalVariationSearch(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    // Check time limit
    if (nodesSearched_ % 1000 == 0)
    {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - searchStartTime_).count();
        if (elapsed >= maxSearchTimeMs_)
        {
            timeUp_ = true;
            return 0;
        }
    }

    if (stopSearch_ || timeUp_)
    {
        return 0;
    }

    // Terminal node
    if (depth == 0)
    {
        return quiescenceSearch(game, alpha, beta, maximizingPlayer);
    }

    // Check for checkmate/stalemate
    std::vector<Move> moves = game.getAllValidMoves();
    if (moves.empty())
    {
        if (game.getBoard().isInCheck(game.getCurrentPlayer()))
        {
            return maximizingPlayer ? -10000 + (maxDepth_ - depth) : 10000 - (maxDepth_ - depth);
        }
        else
        {
            return 0; // Stalemate
        }
    }

    // Transposition table lookup - simplified hash
    uint64_t hash = generateHash(game.getBoard());
    auto ttEntry = transpositionTable_.find(hash);
    if (ttEntry != transpositionTable_.end() && ttEntry->second.depth >= depth)
    {
        ttHits_++;
        return ttEntry->second.score;
    }

    // Order moves
    moves = orderMoves(moves, game);

    int bestScore = maximizingPlayer ? -std::numeric_limits<int>::max() : std::numeric_limits<int>::max();
    Move bestMove;

    for (size_t i = 0; i < moves.size(); ++i)
    {
        const Move &move = moves[i];

        game.makeMove(move);
        int score = principalVariationSearch(game, depth - 1, alpha, beta, !maximizingPlayer);
        game.undoLastMove();

        if (maximizingPlayer)
        {
            if (score > bestScore)
            {
                bestScore = score;
                bestMove = move;
            }
            alpha = std::max(alpha, score);
        }
        else
        {
            if (score < bestScore)
            {
                bestScore = score;
                bestMove = move;
            }
            beta = std::min(beta, score);
        }

        if (beta <= alpha)
        {
            // Update killer moves
            updateKillerMoves(move, depth);
            break;
        }
    }

    // Store in transposition table
    TTEntry entry;
    entry.hash = hash;
    entry.depth = depth;
    entry.score = bestScore;
    entry.bestMove = bestMove;
    entry.flag = TTEntry::EXACT;
    transpositionTable_[hash] = entry;

    return bestScore;
}

int ChessEngine::quiescenceSearch(ChessGame &game, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    // Stand pat evaluation
    int standPat = evaluatePosition(game.getBoard(), game.getCurrentPlayer());
    if (!maximizingPlayer)
        standPat = -standPat;

    if (maximizingPlayer)
    {
        if (standPat >= beta)
            return beta;
        if (standPat > alpha)
            alpha = standPat;
    }
    else
    {
        if (standPat <= alpha)
            return alpha;
        if (standPat < beta)
            beta = standPat;
    }

    // Generate only capture moves
    std::vector<Move> captures;
    std::vector<Move> allMoves = game.getAllValidMoves();
    for (const Move &move : allMoves)
    {
        if (game.getBoard().getPiece(move.getTo()) != nullptr)
        {
            captures.push_back(move);
        }
    }

    // Order captures by SEE
    captures = orderMoves(captures, game);

    for (const Move &capture : captures)
    {
        game.makeMove(capture);
        int score = quiescenceSearch(game, alpha, beta, !maximizingPlayer);
        game.undoLastMove();

        if (maximizingPlayer)
        {
            if (score >= beta)
                return beta;
            if (score > alpha)
                alpha = score;
        }
        else
        {
            if (score <= alpha)
                return alpha;
            if (score < beta)
                beta = score;
        }
    }

    return maximizingPlayer ? alpha : beta;
}

// ===============================================================
// HELPER FUNCTIONS
// ===============================================================

void ChessEngine::updateKillerMoves(const Move &move, int depth)
{
    if (depth >= 0 && depth < 64)
    {
        // Shift killer moves
        killerMoves_[depth][1] = killerMoves_[depth][0];
        killerMoves_[depth][0] = move;
    }
}

void ChessEngine::updateHistoryTable(const Move &move, int depth)
{
    if (move.getFrom().isValid() && move.getTo().isValid())
    {
        int from = move.getFrom().rank * 8 + move.getFrom().file;
        int to = move.getTo().rank * 8 + move.getTo().file;
        if (from >= 0 && from < 64 && to >= 0 && to < 64)
        {
            historyTable_[from][to] += depth * depth;
        }
    }
}

bool ChessEngine::isHashMove(const Move &move) const
{
    // Simple implementation - check if move is in current PV
    return !currentPV_.empty() && currentPV_[0] == move;
}

bool ChessEngine::isKillerMove(const Move &move) const
{
    // Check against killer moves for current depth
    for (int depth = 0; depth < 64; ++depth)
    {
        if (killerMoves_[depth][0] == move || killerMoves_[depth][1] == move)
        {
            return true;
        }
    }
    return false;
}

int ChessEngine::getHistoryScore(const Move &move) const
{
    if (move.getFrom().isValid() && move.getTo().isValid())
    {
        int from = move.getFrom().rank * 8 + move.getFrom().file;
        int to = move.getTo().rank * 8 + move.getTo().file;
        if (from >= 0 && from < 64 && to >= 0 && to < 64)
        {
            return historyTable_[from][to];
        }
    }
    return 0;
}

bool ChessEngine::givesCheck(const ChessGame &game, const Move &move) const
{
    ChessGame tempGame = game;
    tempGame.makeMove(move);
    return tempGame.getBoard().isInCheck(oppositeColor(game.getCurrentPlayer()));
}

int ChessEngine::staticExchangeEvaluation(const ChessGame &game, const Move &move) const
{
    // Simplified SEE implementation
    const Piece *captured = game.getBoard().getPiece(move.getTo());
    const Piece *attacker = game.getBoard().getPiece(move.getFrom());

    if (!captured || !attacker)
    {
        return 0;
    }

    int capturedValue = getPieceValue(captured->getType(), false);
    int attackerValue = getPieceValue(attacker->getType(), false);

    // Simple heuristic: good if we capture more valuable piece
    return capturedValue - attackerValue / 10;
}
