#pragma once

#include "ChessTypes.h"
#include "Move.h"
#include <vector>
#include <string>
#include <unordered_map>
#include <cstdint>
#include <fstream>
#include <iostream>

// Forward declaration
class ChessGame;

// Opening book entry
struct BookEntry
{
    Move move;
    int weight; // Frequency/strength of this move

    BookEntry(const Move &m, int w) : move(m), weight(w) {}
};

class OpeningBook
{
private:
    // Map from position hash to list of book moves
    std::unordered_map<uint64_t, std::vector<BookEntry>> bookMoves_;
    bool isLoaded_;

    // Simple opening moves database (hardcoded for now)
    void loadHardcodedOpenings();

    // Hash function for positions
    uint64_t generatePositionHash(const ChessGame &game) const;

public:
    OpeningBook();

    // Load opening book from file
    bool loadFromFile(const std::string &filename);

    // Load Polyglot format book
    bool loadPolyglotBook(const std::string &filename);

    // Convert Polyglot move format to our Move format
    Move convertPolyglotMove(uint16_t polyglotMove) const;

    // Get a book move for current position
    Move getBookMove(const ChessGame &game) const;

    // Check if position is in book
    bool hasBookMove(const ChessGame &game) const;

    // Enhanced opening book methods
    bool hasMove(const ChessGame &game) const;
    Move getMove(const ChessGame &game) const;

    // Get all book moves for position
    std::vector<BookEntry> getBookMoves(const ChessGame &game) const;

    // Statistics
    int getBookSize() const { return bookMoves_.size(); }
    bool isLoaded() const { return isLoaded_; }
};
