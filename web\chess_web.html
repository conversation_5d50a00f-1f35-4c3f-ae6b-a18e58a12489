<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VibeChess Engine - Web Version</title>
    <!--
    VibeChess Engine Web Interface with FEN Support
    Features:
    - Play against AI engine (Depth 6 default)
    - Load custom positions via FEN notation
    - Quick preset positions (openings, endgames)
    - Real-time FEN validation
    - Copy current position to FEN
    - Move history and analysis
    -->
    <!-- Import chess.js from CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.2/chess.js"></script>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            border: 2px solid #00ff00;
            padding: 20px;
            margin-bottom: 20px;
        }

        .game-area {
            display: flex;
            gap: 20px;
        }

        .board-container {
            flex: 1;
        }

        .board-wrapper {
            display: inline-block;
            position: relative;
            margin: 20px;
        }

        .chess-board {
            display: grid;
            grid-template-columns: repeat(8, 60px);
            grid-template-rows: repeat(8, 60px);
            border: 2px solid #00ff00;
            position: relative;
        }

        .coordinates {
            position: absolute;
            color: #00ff00;
            font-weight: bold;
            font-size: 14px;
        }

        .rank-labels {
            left: -25px;
            top: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
        }

        .file-labels {
            bottom: -25px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .rank-label,
        .file-label {
            width: 60px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .square {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 1px solid #333;
            position: relative;
            box-sizing: border-box;
        }

        .light {
            background-color: #f0d9b5;
            color: #000;
        }

        .dark {
            background-color: #b58863;
            color: #000;
        }

        .selected {
            border: 3px solid #ffcc00 !important;
        }

        .possible-move {
            background-color: #90EE90 !important;
        }

        .check {
            background-color: #ff4444 !important;
            box-shadow: 0 0 10px #ff0000;
        }

        .game-over {
            background-color: #ff6b6b !important;
            animation: gameOverBlink 1s infinite;
        }

        @keyframes gameOverBlink {

            0%,
            50% {
                opacity: 1;
            }

            51%,
            100% {
                opacity: 0.7;
            }
        }

        .piece {
            width: 50px;
            height: 50px;
            background-size: contain;
            background-repeat: no-repeat;
            cursor: pointer;
        }

        .coordinate {
            position: absolute;
            font-size: 10px;
            color: #333;
            pointer-events: none;
        }

        .rank {
            top: 2px;
            left: 2px;
        }

        .file {
            bottom: 2px;
            right: 2px;
        }

        .control-panel {
            flex: 1;
            border: 2px solid #00ff00;
            padding: 20px;
        }

        .engine-config {
            margin-bottom: 20px;
        }

        .config-item {
            margin: 10px 0;
        }

        .config-item label {
            display: inline-block;
            width: 120px;
        }

        .config-item input,
        .config-item select {
            background-color: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 5px;
        }

        .fen-input {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background-color: #222;
            color: #00ff00;
            border: 2px solid #00ff00;
            border-radius: 4px;
        }

        .fen-buttons {
            display: flex;
            gap: 5px;
            margin: 5px 0;
        }

        .fen-buttons button {
            flex: 1;
            padding: 8px;
            background-color: #333;
            color: #00ff00;
            border: 1px solid #00ff00;
            border-radius: 4px;
            cursor: pointer;
        }

        .fen-buttons button:hover {
            background-color: #00ff00;
            color: #000;
        }

        .move-history {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            background-color: #333;
        }

        .engine-output {
            height: 150px;
            overflow-y: auto;
            border: 1px solid #00ff00;
            padding: 10px;
            margin: 10px 0;
            background-color: #333;
            font-size: 12px;
        }

        button {
            background-color: #333;
            color: #00ff00;
            border: 2px solid #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background-color: #00ff00;
            color: #000;
        }

        .status {
            padding: 10px;
            border: 1px solid #00ff00;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏆 VibeChess Engine - Web Version</h1>
            <p>Play against the VibeChess AI engine in your browser!</p>
        </div>

        <div class="game-area">
            <div class="board-container">
                <div class="status" id="gameStatus">
                    Game Status: Ready to play | Your turn (White)
                </div>

                <div class="status" id="gameOverStatus"
                    style="display: none; background-color: #ff4444; color: white; font-weight: bold; text-align: center; padding: 15px; margin: 10px 0; border: 2px solid #ff0000; animation: gameOverBlink 1s infinite;">
                    🏁 GAME OVER 🏁
                </div>

                <div class="board-wrapper">
                    <!-- Rank labels (8,7,6,5,4,3,2,1) -->
                    <div class="coordinates rank-labels">
                        <div class="rank-label">8</div>
                        <div class="rank-label">7</div>
                        <div class="rank-label">6</div>
                        <div class="rank-label">5</div>
                        <div class="rank-label">4</div>
                        <div class="rank-label">3</div>
                        <div class="rank-label">2</div>
                        <div class="rank-label">1</div>
                    </div>

                    <div class="chess-board" id="chessBoard">
                        <!-- Board will be generated by JavaScript -->
                    </div>

                    <!-- File labels (a,b,c,d,e,f,g,h) -->
                    <!-- <div class="coordinates file-labels">
                        <div class="file-label">a</div>
                        <div class="file-label">b</div>
                        <div class="file-label">c</div>
                        <div class="file-label">d</div>
                        <div class="file-label">e</div>
                        <div class="file-label">f</div>
                        <div class="file-label">g</div>
                        <div class="file-label">h</div>
                    </div> -->
                </div>

                <div class="move-input">
                    <label>Manual Move: </label>
                    <input type="text" id="moveInput" placeholder="e2e4" maxlength="5">
                    <button onclick="makeManualMove()">Make Move</button>
                </div>
            </div>

            <div class="control-panel">
                <h3>🔧 Engine Configuration</h3>
                <div class="engine-config">
                    <div class="config-item">
                        <label>Difficulty:</label>
                        <select id="engineDepth" onchange="updateEngineConfig()">
                            <option value="2">Beginner (Depth 2)</option>
                            <option value="3">Easy (Depth 3)</option>
                            <option value="4">Normal (Depth 4)</option>
                            <option value="5">Hard (Depth 5)</option>
                            <option value="6" selected>Expert (Depth 6)</option>
                            <option value="7">Master (Depth 7)</option>
                            <option value="8">Grandmaster (Depth 8)</option>
                        </select>
                    </div>

                    <div class="config-item">
                        <label>Think Time:</label>
                        <select id="thinkTime" onchange="updateEngineConfig()">
                            <option value="1000">1 second</option>
                            <option value="3000" selected>3 seconds</option>
                            <option value="5000">5 seconds</option>
                            <option value="10000">10 seconds</option>
                        </select>
                    </div>

                    <div class="config-item">
                        <label>Your Color:</label>
                        <select id="playerColor" onchange="newGame()">
                            <option value="white" selected>White</option>
                            <option value="black">Black</option>
                        </select>
                    </div>
                </div>

                <h3>📋 FEN Position</h3>
                <div class="engine-config">
                    <div class="config-item">
                        <label>Load FEN:</label>
                        <input type="text" id="fenInput" class="fen-input"
                            placeholder="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
                            oninput="validateFEN()">
                        <div id="fenStatus" style="font-size: 12px; margin: 2px 0; color: #888;"></div>
                        <div class="fen-buttons">
                            <button onclick="loadFEN()">📥 Load FEN</button>
                            <button onclick="copyFEN()">📋 Copy Current FEN</button>
                        </div>
                    </div>

                    <div class="config-item">
                        <label>Quick Positions:</label>
                        <select id="presetPositions" onchange="loadPresetPosition()"
                            style="width: 100%; margin: 5px 0;">
                            <option value="">-- Select Preset Position --</option>
                            <option value="rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1">Starting Position
                            </option>
                            <option value="r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R b KQkq - 0 4">Italian
                                Game</option>
                            <option value="rnbqkb1r/ppp1pppp/5n2/3p4/3PP3/8/PPP2PPP/RNBQKBNR w KQkq d6 0 3">Scandinavian
                                Defense</option>
                            <option value="r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4">
                                Italian Game - Classical</option>
                            <option value="rnbqk2r/pppp1ppp/5n2/2b1p3/2B1P3/8/PPPP1PPP/RNBQK1NR w KQkq - 4 4">Italian
                                Game - Hungarian Defense</option>
                            <option value="r1bqkbnr/pppp1ppp/2n5/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3">Four
                                Knights Game</option>
                            <option value="rnbqkb1r/pp1ppppp/5n2/2p5/2B1P3/8/PPPP1PPP/RNBQK1NR w KQkq c6 0 3">Sicilian
                                Defense</option>
                            <option value="rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq d6 0 2">Queen's Pawn
                                Game</option>
                            <option value="r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 4 4">
                                Spanish Opening</option>
                            <option value="8/8/8/8/8/8/8/4K2k w - - 0 1">King vs King Endgame</option>
                            <option value="8/8/8/8/8/8/6PP/6K1 w - - 0 1">King and Pawns Endgame</option>
                            <option value="8/8/8/8/3k4/8/3K4/3Q4 w - - 0 1">Queen vs King Endgame</option>
                            <option value="8/8/8/8/3k4/8/3K4/7R w - - 0 1">Rook vs King Endgame</option>
                            <option value="r1bq1rk1/ppp2ppp/2n2n2/2bpp3/2B1P3/3P1N2/PPP2PPP/RNBQ1RK1 w - - 0 6">
                                Middlegame Position</option>
                            <option value="8/2p5/3p4/KP5r/1R3p1k/8/4P1P1/8 w - - 0 1">Complex Endgame</option>
                        </select>
                    </div>
                </div>

                <div class="game-controls">
                    <button onclick="newGame()">🆕 New Game</button>
                    <button onclick="undoMove()">↶ Undo Move</button>
                    <button onclick="flipBoard()">🔄 Flip Board</button>
                    <button onclick="resignGame()" style="background-color: #ff4444; border-color: #ff0000;">🏳️
                        Resign</button>
                    <button onclick="getHint()">💡 Hint</button>
                    <button onclick="analyzePosition()">🔍 Analyze</button>
                </div>

                <h4>📝 Move History</h4>
                <div class="move-history" id="moveHistory">
                    Game started. Make your first move!
                </div>

                <h4>🤖 Engine Output</h4>
                <div class="engine-output" id="engineOutput">
                    VibeChess Engine ready...<br>
                    Depth: 6, Time: 3s<br>
                    Waiting for your move...
                </div>

                <div class="status">
                    <strong>Engine Status:</strong> <span id="engineStatus">Ready</span><br>
                    <strong>Last Evaluation:</strong> <span id="lastEval">+0.00</span><br>
                    <strong>Nodes Searched:</strong> <span id="nodesSearched">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Chess engine web interface using chess.js
        class ChessEngineWeb {
            constructor() {
                this.game = new Chess();
                this.playerColor = 'white';
                this.engineDepth = 6;
                this.thinkTime = 3000;
                this.selectedSquare = null;
                this.boardFlipped = false;
                this.gameResigned = null;

                // Piece images mapping
                this.pieceImages = {
                    'p': 'black_pawn.png', 'r': 'black_rook.png', 'n': 'black_knight.png',
                    'b': 'black_bishop.png', 'q': 'black_queen.png', 'k': 'black_king.png',
                    'P': 'white_pawn.png', 'R': 'white_rook.png', 'N': 'white_knight.png',
                    'B': 'white_bishop.png', 'Q': 'white_queen.png', 'K': 'white_king.png'
                };

                this.initializeUI();
                this.renderBoard();
            }

            // Helper: convert FEN string to 2D board array
            fenToBoard(fen) {
                const rows = fen.split(' ')[0].split('/');
                const board = [];
                for (const rowStr of rows) {
                    const row = [];
                    for (const ch of rowStr) {
                        if (!isNaN(ch)) {
                            // empty squares
                            const empties = parseInt(ch, 10);
                            for (let i = 0; i < empties; i++) row.push(null);
                        } else {
                            const color = ch === ch.toUpperCase() ? 'w' : 'b';
                            const type = ch.toLowerCase();
                            row.push({ color, type });
                        }
                    }
                    board.push(row);
                }
                return board;
            }

            initializeUI() {
                // Board initialization will be done in renderBoard
            }

            renderBoard() {
                const boardElement = document.getElementById('chessBoard');
                boardElement.innerHTML = '';
                const boardData = this.fenToBoard(this.game.fen());

                for (let row = 0; row < 8; row++) {
                    const boardRow = this.boardFlipped ? 7 - row : row;
                    for (let col = 0; col < 8; col++) {
                        const boardCol = this.boardFlipped ? 7 - col : col;

                        const square = document.createElement('div');
                        const squareName = 'abcdefgh'[boardCol] + (8 - boardRow);
                        square.id = squareName;
                        square.classList.add('square', (row + col) % 2 === 0 ? 'light' : 'dark');
                        square.onclick = () => this.handleSquareClick(squareName);

                        const piece = boardData[boardRow][boardCol];
                        if (piece) {
                            const pieceElement = document.createElement('div');
                            pieceElement.classList.add('piece');
                            const symbol = piece.color === 'w' ? piece.type.toUpperCase() : piece.type.toLowerCase();
                            pieceElement.style.backgroundImage = `url(../pieces/${this.pieceImages[symbol]})`;
                            square.appendChild(pieceElement);
                        }

                        // Coordinate labels
                        if (!this.boardFlipped) {
                            if (col === 0) {
                                const rankEl = document.createElement('span');
                                rankEl.textContent = 8 - row;
                                rankEl.classList.add('coordinate', 'rank');
                                square.appendChild(rankEl);
                            }
                            if (row === 7) {
                                const fileEl = document.createElement('span');
                                fileEl.textContent = 'abcdefgh'[col];
                                fileEl.classList.add('coordinate', 'file');
                                square.appendChild(fileEl);
                            }
                        } else {
                            if (col === 0) {
                                const rankEl = document.createElement('span');
                                rankEl.textContent = row + 1;
                                rankEl.classList.add('coordinate', 'rank');
                                square.appendChild(rankEl);
                            }
                            if (row === 7) {
                                const fileEl = document.createElement('span');
                                fileEl.textContent = 'abcdefgh'[7 - col];
                                fileEl.classList.add('coordinate', 'file');
                                square.appendChild(fileEl);
                            }
                        }
                        boardElement.appendChild(square);
                    }
                }
                this.highlightCheck();
                this.updateGameStatus();
            }

            handleSquareClick(squareName) {
                if (this.game.game_over() || this.gameResigned) return;
                if (this.game.turn() !== (this.playerColor === 'white' ? 'w' : 'b')) return;

                if (this.selectedSquare) {
                    // Try to make a move
                    const move = this.game.move({
                        from: this.selectedSquare,
                        to: squareName,
                        promotion: 'q'
                    });

                    // Clear selection highlighting
                    if (document.getElementById(this.selectedSquare)) {
                        document.getElementById(this.selectedSquare).classList.remove('selected');
                    }
                    this.clearPossibleMoves();
                    this.selectedSquare = null;

                    if (move) {
                        this.renderBoard();
                        this.updateMoveHistory();
                        this.updateFENField();
                        if (!this.game.game_over()) {
                            setTimeout(() => this.makeEngineMove(), 500);
                        }
                    }
                } else {
                    // Select piece if it belongs to current player
                    const piece = this.game.get(squareName);
                    if (piece && piece.color === (this.playerColor === 'white' ? 'w' : 'b')) {
                        this.selectedSquare = squareName;
                        document.getElementById(squareName).classList.add('selected');
                        this.showPossibleMoves(squareName);
                    }
                }
            }

            clearPossibleMoves() {
                const squares = document.querySelectorAll('.square');
                squares.forEach(square => {
                    square.classList.remove('possible-move');
                });
            }

            highlightCheck() {
                // Clear previous check highlights
                const squares = document.querySelectorAll('.square');
                squares.forEach(square => {
                    square.classList.remove('check');
                });

                // Highlight king in check
                if (this.game.in_check()) {
                    const currentPlayer = this.game.turn();
                    // Find the king position
                    for (let file of 'abcdefgh') {
                        for (let rank of '12345678') {
                            const square = file + rank;
                            const piece = this.game.get(square);
                            if (piece && piece.type === 'k' && piece.color === currentPlayer) {
                                const squareElement = document.getElementById(square);
                                if (squareElement) {
                                    squareElement.classList.add('check');
                                }
                                return;
                            }
                        }
                    }
                }
            }

            showPossibleMoves(squareName) {
                const moves = this.game.moves({ square: squareName, verbose: true });
                moves.forEach(move => {
                    const targetSquare = document.getElementById(move.to);
                    if (targetSquare) {
                        targetSquare.classList.add('possible-move');
                    }
                });
            }

            updateGameStatus() {
                const statusElement = document.getElementById('gameStatus');
                const gameOverElement = document.getElementById('gameOverStatus');

                if (this.game.game_over() || this.gameResigned) {
                    // Show game over indicator
                    gameOverElement.style.display = 'block';

                    if (this.gameResigned) {
                        statusElement.textContent = `Game Status: ${this.gameResigned} resigned! Game Over.`;
                        gameOverElement.innerHTML = `🏳️ ${this.gameResigned.toUpperCase()} RESIGNED 🏳️`;
                    } else if (this.game.in_checkmate()) {
                        const winner = this.game.turn() === 'w' ? 'Black' : 'White';
                        statusElement.textContent = `Game Status: Checkmate! ${winner} wins.`;
                        gameOverElement.innerHTML = `♔ CHECKMATE! ${winner.toUpperCase()} WINS! ♔`;
                    } else if (this.game.in_stalemate()) {
                        statusElement.textContent = 'Game Status: Stalemate! Draw.';
                        gameOverElement.innerHTML = `🤝 STALEMATE - DRAW! 🤝`;
                    } else if (this.game.in_threefold_repetition()) {
                        statusElement.textContent = 'Game Status: Draw by threefold repetition.';
                        gameOverElement.innerHTML = `🔄 DRAW - THREEFOLD REPETITION! 🔄`;
                    } else if (this.game.insufficient_material()) {
                        statusElement.textContent = 'Game Status: Draw by insufficient material.';
                        gameOverElement.innerHTML = `⚖️ DRAW - INSUFFICIENT MATERIAL! ⚖️`;
                    } else if (this.game.in_draw()) {
                        statusElement.textContent = 'Game Status: Draw!';
                        gameOverElement.innerHTML = `🤝 DRAW! 🤝`;
                    }
                } else {
                    // Hide game over indicator
                    gameOverElement.style.display = 'none';

                    const currentPlayerName = this.game.turn() === (this.playerColor === 'white' ? 'w' : 'b') ? 'Your' : 'Engine\'s';
                    const colorName = this.game.turn() === 'w' ? 'White' : 'Black';
                    const checkStatus = this.game.in_check() ? ' - IN CHECK!' : '';
                    statusElement.textContent = `Game Status: Playing | ${currentPlayerName} turn (${colorName})${checkStatus}`;
                }
            }

            makeEngineMove() {
                this.updateEngineStatus('Thinking...');
                this.addEngineOutput('Engine is analyzing position...');

                // Simulate engine thinking
                setTimeout(() => {
                    const possibleMoves = this.game.moves();
                    if (possibleMoves.length > 0) {
                        // Simple random move for demo
                        const randomMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];
                        const move = this.game.move(randomMove);

                        if (move) {
                            this.renderBoard();
                            this.updateMoveHistory();
                            this.updateFENField();

                            // Simulate engine output
                            this.addEngineOutput(`Best move: ${move.san}`);
                            this.addEngineOutput(`Evaluation: +0.25`);
                            this.addEngineOutput(`Nodes searched: 15,432`);

                            this.updateEngineStatus('Ready');
                            document.getElementById('lastEval').textContent = '+0.25';
                            document.getElementById('nodesSearched').textContent = '15,432';
                        }
                    } else {
                        this.addEngineOutput('No legal moves available');
                        this.updateEngineStatus('Game Over');
                    }
                }, this.thinkTime);
            }

            updateMoveHistory() {
                const historyElement = document.getElementById('moveHistory');
                const history = this.game.history({ verbose: true });

                let historyText = '';
                for (let i = 0; i < history.length; i += 2) {
                    const moveNumber = Math.floor(i / 2) + 1;
                    const whiteMove = history[i] ? history[i].san : '';
                    const blackMove = history[i + 1] ? history[i + 1].san : '';
                    historyText += `${moveNumber}. ${whiteMove} ${blackMove}<br>`;
                }

                historyElement.innerHTML = historyText || 'New game started!';
                historyElement.scrollTop = historyElement.scrollHeight;
            }

            updateEngineStatus(status) {
                document.getElementById('engineStatus').textContent = status;
            }

            addEngineOutput(text) {
                const outputElement = document.getElementById('engineOutput');
                outputElement.innerHTML += text + '<br>';
                outputElement.scrollTop = outputElement.scrollHeight;
            }

            updateFENField() {
                const fenInput = document.getElementById('fenInput');
                if (fenInput) {
                    fenInput.value = this.game.fen();
                }
            }

            newGame() {
                this.game.reset();
                this.playerColor = document.getElementById('playerColor').value;
                this.selectedSquare = null;
                this.boardFlipped = this.playerColor === 'black';
                this.gameResigned = null; // Reset resignation status

                this.renderBoard();
                this.updateMoveHistory();
                this.updateFENField();
                this.updateEngineStatus('Ready');

                document.getElementById('engineOutput').innerHTML = 'VibeChess Engine ready...<br>Depth: ' + this.engineDepth + ', Time: ' + (this.thinkTime / 1000) + 's<br>';

                if (this.playerColor === 'black') {
                    setTimeout(() => this.makeEngineMove(), 1000);
                }
            }

            resignGame() {
                if (this.game.game_over() || this.gameResigned) {
                    this.addEngineOutput('Game is already over!');
                    return;
                }

                const playerColorName = this.playerColor === 'white' ? 'White' : 'Black';
                this.gameResigned = playerColorName;
                this.addEngineOutput(`${playerColorName} resigned! Game over.`);
                this.updateGameStatus();
            }

            undoMove() {
                const history = this.game.history();
                if (history.length >= 2) {
                    // Undo last two moves (player + engine)
                    this.game.undo();
                    this.game.undo();
                    this.renderBoard();
                    this.updateMoveHistory();
                    this.updateFENField();
                    this.addEngineOutput('Undid last two moves');
                } else if (history.length === 1) {
                    this.game.undo();
                    this.renderBoard();
                    this.updateMoveHistory();
                    this.updateFENField();
                    this.addEngineOutput('Undid last move');
                } else {
                    this.addEngineOutput('No moves to undo');
                }
            }

            getHint() {
                this.addEngineOutput('Analyzing for best move...');
                setTimeout(() => {
                    const possibleMoves = this.game.moves();
                    if (possibleMoves.length > 0) {
                        const randomHint = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];
                        this.addEngineOutput(`Hint: Consider ${randomHint}`);
                    } else {
                        this.addEngineOutput('No legal moves available');
                    }
                }, 1000);
            }

            analyzePosition() {
                this.addEngineOutput('Deep analysis started...');
                setTimeout(() => {
                    this.addEngineOutput('Position evaluation: +0.15 (slightly better for white)');
                    const possibleMoves = this.game.moves();
                    if (possibleMoves.length > 0) {
                        const bestMove = possibleMoves[0]; // Simple: just take first move
                        this.addEngineOutput(`Best move: ${bestMove}`);
                    }
                    this.addEngineOutput('Key features: Central control, piece development');
                }, 2000);
            }
        }

        // Global functions
        function updateEngineConfig() {
            const depth = document.getElementById('engineDepth').value;
            const time = document.getElementById('thinkTime').value;

            game.engineDepth = parseInt(depth);
            game.thinkTime = parseInt(time);

            game.addEngineOutput(`Configuration updated: Depth=${depth}, Time=${time}ms`);
        }

        function makeManualMove() {
            const moveInput = document.getElementById('moveInput');
            const moveStr = moveInput.value.toLowerCase();

            if (moveStr.length >= 4) {
                try {
                    const move = game.game.move(moveStr, { sloppy: true });
                    if (move) {
                        game.renderBoard();
                        game.updateMoveHistory();
                        moveInput.value = '';

                        if (!game.game.game_over()) {
                            setTimeout(() => game.makeEngineMove(), 500);
                        }
                    } else {
                        game.addEngineOutput('Invalid move: ' + moveStr);
                    }
                } catch (error) {
                    game.addEngineOutput('Invalid move: ' + moveStr);
                }
            } else {
                game.addEngineOutput('Invalid move format. Use format like: e2e4');
            }
        }

        function newGame() {
            game.newGame();
        }

        function undoMove() {
            game.undoMove();
        }

        function getHint() {
            game.getHint();
        }

        function analyzePosition() {
            game.analyzePosition();
        }

        function flipBoard() {
            game.boardFlipped = !game.boardFlipped;
            game.renderBoard();
        }

        function resignGame() {
            game.resignGame();
        }

        // FEN Support Functions
        function loadFEN() {
            const fenInput = document.getElementById('fenInput');
            const fenString = fenInput.value.trim();

            if (!fenString) {
                game.addEngineOutput('Please enter a FEN string');
                return;
            }

            try {
                // Validate and load FEN
                const testGame = new Chess(fenString);

                // If successful, load into main game
                game.game.load(fenString);
                game.renderBoard();
                game.updateMoveHistory();
                game.updateGameStatus();

                game.addEngineOutput(`FEN loaded successfully: ${fenString}`);
                game.addEngineOutput(`Position: ${game.game.turn() === 'w' ? 'White' : 'Black'} to move`);

                // Update FEN input to show current position
                document.getElementById('fenInput').value = game.game.fen();

                // Reset preset selection
                document.getElementById('presetPositions').value = '';

            } catch (error) {
                game.addEngineOutput(`Invalid FEN: ${error.message}`);
            }
        }

        function copyFEN() {
            const currentFEN = game.game.fen();

            // Copy to clipboard
            navigator.clipboard.writeText(currentFEN).then(() => {
                game.addEngineOutput(`FEN copied to clipboard: ${currentFEN}`);
            }).catch(() => {
                // Fallback for older browsers
                const fenInput = document.getElementById('fenInput');
                fenInput.value = currentFEN;
                fenInput.select();
                document.execCommand('copy');
                game.addEngineOutput(`FEN copied: ${currentFEN}`);
            });
        }

        function loadPresetPosition() {
            const presetSelect = document.getElementById('presetPositions');
            const selectedFEN = presetSelect.value;

            if (!selectedFEN) return;

            try {
                // Load the preset FEN
                game.game.load(selectedFEN);
                game.renderBoard();
                game.updateMoveHistory();
                game.updateGameStatus();

                // Update FEN input field
                document.getElementById('fenInput').value = selectedFEN;

                const positionName = presetSelect.options[presetSelect.selectedIndex].text;
                game.addEngineOutput(`Loaded preset: ${positionName}`);
                game.addEngineOutput(`Position: ${game.game.turn() === 'w' ? 'White' : 'Black'} to move`);

                // Update FEN status
                validateFEN();

            } catch (error) {
                game.addEngineOutput(`Error loading preset: ${error.message}`);
            }
        }

        function validateFEN() {
            const fenInput = document.getElementById('fenInput');
            const fenStatus = document.getElementById('fenStatus');
            const fenString = fenInput.value.trim();

            if (!fenString) {
                fenStatus.textContent = '';
                return;
            }

            try {
                const testGame = new Chess(fenString);
                const turn = testGame.turn() === 'w' ? 'White' : 'Black';
                const inCheck = testGame.in_check() ? ' (IN CHECK)' : '';
                const gameOver = testGame.game_over() ? ' - GAME OVER' : '';

                fenStatus.style.color = '#00ff00';
                fenStatus.textContent = `✓ Valid FEN - ${turn} to move${inCheck}${gameOver}`;

            } catch (error) {
                fenStatus.style.color = '#ff4444';
                fenStatus.textContent = `✗ Invalid FEN: ${error.message}`;
            }
        }

        // Initialize game when page loads
        let game;
        window.onload = function () {
            game = new ChessEngineWeb();
            // Initialize engine output
            game.addEngineOutput('VibeChess Engine ready...');
            game.addEngineOutput('Depth: ' + game.engineDepth + ', Time: ' + (game.thinkTime / 1000) + 's');
            game.addEngineOutput('Waiting for your move...');

            // Initialize FEN field with starting position
            game.updateFENField();
        };
    </script>
</body>

</html>