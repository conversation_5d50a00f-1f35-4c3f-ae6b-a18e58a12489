#include "OpeningBook.h"
#include "ChessGame.h"
#include "ZobristHash.h"
#include <random>
#include <algorithm>
#include <iostream>

OpeningBook::OpeningBook() : isLoaded_(false)
{
    loadHardcodedOpenings();
}

void OpeningBook::loadHardcodedOpenings()
{
    // Common opening moves with their frequencies
    // This is a simplified implementation - in production, load from database

    // Starting position hash (simplified)
    uint64_t startingHash = 0x123456789ABCDEF0ULL; // Placeholder hash

    // Popular first moves for White
    std::vector<BookEntry> whiteMoves = {
        BookEntry(Move(Position(1, 4), Position(3, 4)), 100), // e2-e4 (King's Pawn)
        BookEntry(Move(Position(1, 3), Position(3, 3)), 90),  // d2-d4 (Queen's Pawn)
        BookEntry(Move(Position(0, 6), Position(2, 5)), 70),  // Ng1-f3 (<PERSON><PERSON>)
        BookEntry(Move(Position(1, 2), Position(3, 2)), 50),  // c2-c4 (English)
        BookEntry(Move(Position(1, 6), Position(2, 6)), 30),  // g2-g3 (<PERSON>'s Indian Attack)
    };

    bookMoves_[startingHash] = whiteMoves;

    // Add some responses to e2-e4
    uint64_t afterE4Hash = 0x123456789ABCDEF1ULL; // Hash after 1.e4
    std::vector<BookEntry> blackResponses = {
        BookEntry(Move(Position(6, 4), Position(4, 4)), 100), // e7-e5 (King's Pawn Game)
        BookEntry(Move(Position(6, 2), Position(4, 2)), 80),  // c7-c5 (Sicilian)
        BookEntry(Move(Position(6, 4), Position(5, 4)), 60),  // e7-e6 (French)
        BookEntry(Move(Position(6, 2), Position(5, 2)), 50),  // c7-c6 (Caro-Kann)
        BookEntry(Move(Position(6, 3), Position(4, 3)), 40),  // d7-d5 (Scandinavian)
    };

    bookMoves_[afterE4Hash] = blackResponses;

    // Add some responses to d2-d4
    uint64_t afterD4Hash = 0x123456789ABCDEF2ULL; // Hash after 1.d4
    std::vector<BookEntry> blackResponsesD4 = {
        BookEntry(Move(Position(6, 3), Position(4, 3)), 100), // d7-d5 (Queen's Gambit)
        BookEntry(Move(Position(7, 6), Position(5, 5)), 80),  // Ng8-f6 (Indian Defenses)
        BookEntry(Move(Position(6, 5), Position(4, 5)), 60),  // f7-f5 (Dutch)
        BookEntry(Move(Position(6, 4), Position(5, 4)), 50),  // e7-e6 (Queen's Pawn Game)
    };

    bookMoves_[afterD4Hash] = blackResponsesD4;

    isLoaded_ = true;
    // Debug output removed for UCI compatibility
    // std::cout << "Opening book loaded with " << bookMoves_.size() << " positions.\n";
}

uint64_t OpeningBook::generatePositionHash(const ChessGame &game) const
{
    const ChessBoard &board = game.getBoard();
    Color currentPlayer = game.getCurrentPlayer();

    // Calculate castling rights as integer
    int castlingRights = 0;
    if (board.canCastleKingside(Color::WHITE))
        castlingRights |= 1;
    if (board.canCastleQueenside(Color::WHITE))
        castlingRights |= 2;
    if (board.canCastleKingside(Color::BLACK))
        castlingRights |= 4;
    if (board.canCastleQueenside(Color::BLACK))
        castlingRights |= 8;

    // Get en passant file
    int enPassantFile = -1;
    Position enPassantTarget = board.getEnPassantTarget();
    if (enPassantTarget.isValid())
    {
        enPassantFile = enPassantTarget.file;
    }

    // Use proper Zobrist hashing
    return ZobristHash::generateHash(board, currentPlayer, castlingRights, enPassantFile);
}

Move OpeningBook::getBookMove(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return Move(); // Invalid move
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);

    if (it == bookMoves_.end() || it->second.empty())
    {
        return Move(); // No book move
    }

    const std::vector<BookEntry> &moves = it->second;

    // DETERMINISTIC SELECTION: Choose move with highest weight
    // No more random selection for consistent play!

    int bestWeight = -1;
    Move bestMove;

    for (const auto &entry : moves)
    {
        if (entry.weight > bestWeight)
        {
            bestWeight = entry.weight;
            bestMove = entry.move;
        }
    }

    if (bestMove.isValid())
    {
        return bestMove;
    }

    // Fallback to first move
    return moves[0].move;
}

bool OpeningBook::hasBookMove(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return false;
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);
    return it != bookMoves_.end() && !it->second.empty();
}

std::vector<BookEntry> OpeningBook::getBookMoves(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return {};
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);

    if (it != bookMoves_.end())
    {
        return it->second;
    }

    return {};
}

bool OpeningBook::loadFromFile(const std::string &filename)
{
    if (filename.empty())
    {
        std::cout << "Loading from file not yet implemented. Using hardcoded openings.\n";
        return isLoaded_;
    }

    // Try to load Polyglot book format (.bin files)
    if (filename.find(".bin") != std::string::npos)
    {
        return loadPolyglotBook(filename);
    }

    std::cout << "Unsupported book format. Using hardcoded openings.\n";
    return isLoaded_;
}

bool OpeningBook::loadPolyglotBook(const std::string &filename)
{
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open())
    {
        std::cout << "Failed to open opening book: " << filename << std::endl;
        return false;
    }

    bookMoves_.clear();

    struct PolyglotEntry
    {
        uint64_t key;
        uint16_t move;
        uint16_t weight;
        uint32_t learn;
    };

    PolyglotEntry entry;
    int entriesLoaded = 0;

    while (file.read(reinterpret_cast<char *>(&entry), sizeof(PolyglotEntry)))
    {
        // Convert from big-endian (Polyglot format is big-endian)
        uint64_t key = __builtin_bswap64(entry.key);
        uint16_t moveData = __builtin_bswap16(entry.move);
        uint16_t weight = __builtin_bswap16(entry.weight);

        // Convert Polyglot move format to our Move format
        Move move = convertPolyglotMove(moveData);
        if (move.isValid())
        {
            bookMoves_[key].emplace_back(move, weight);
            entriesLoaded++;
        }
    }

    file.close();
    isLoaded_ = true;

    std::cout << "Loaded " << entriesLoaded << " moves from opening book: " << filename << std::endl;
    return true;
}

Move OpeningBook::convertPolyglotMove(uint16_t polyglotMove) const
{
    // Polyglot move format:
    // bits 0-5: from square (0-63)
    // bits 6-11: to square (0-63)
    // bits 12-14: promotion piece (0=none, 1=knight, 2=bishop, 3=rook, 4=queen)

    int fromSquare = polyglotMove & 0x3F;
    int toSquare = (polyglotMove >> 6) & 0x3F;
    int promotion = (polyglotMove >> 12) & 0x7;

    // Convert square indices to Position objects
    Position from(fromSquare / 8, fromSquare % 8);
    Position to(toSquare / 8, toSquare % 8);

    // Handle promotion
    PieceType promotionType = PieceType::NONE;
    if (promotion > 0)
    {
        switch (promotion)
        {
        case 1:
            promotionType = PieceType::KNIGHT;
            break;
        case 2:
            promotionType = PieceType::BISHOP;
            break;
        case 3:
            promotionType = PieceType::ROOK;
            break;
        case 4:
            promotionType = PieceType::QUEEN;
            break;
        }
    }

    return Move(from, to, promotionType);
}
