#include "OpeningBook.h"
#include "ChessGame.h"
#include <random>
#include <algorithm>
#include <iostream>

OpeningBook::OpeningBook() : isLoaded_(false)
{
    loadHardcodedOpenings();
}

void OpeningBook::loadHardcodedOpenings()
{
    // Common opening moves with their frequencies
    // This is a simplified implementation - in production, load from database

    // Starting position hash (simplified)
    uint64_t startingHash = 0x123456789ABCDEF0ULL; // Placeholder hash

    // Popular first moves for White
    std::vector<BookEntry> whiteMoves = {
        BookEntry(Move(Position(1, 4), Position(3, 4)), 100), // e2-e4 (King's Pawn)
        BookEntry(Move(Position(1, 3), Position(3, 3)), 90),  // d2-d4 (Queen's Pawn)
        BookEntry(Move(Position(0, 6), Position(2, 5)), 70),  // Ng1-f3 (Reti)
        BookEntry(Move(Position(1, 2), Position(3, 2)), 50),  // c2-c4 (English)
        BookEntry(Move(Position(1, 6), Position(2, 6)), 30),  // g2-g3 (<PERSON>'s Indian Attack)
    };

    bookMoves_[startingHash] = whiteMoves;

    // Add some responses to e2-e4
    uint64_t afterE4Hash = 0x123456789ABCDEF1ULL; // Hash after 1.e4
    std::vector<BookEntry> blackResponses = {
        BookEntry(Move(Position(6, 4), Position(4, 4)), 100), // e7-e5 (King's Pawn Game)
        BookEntry(Move(Position(6, 2), Position(4, 2)), 80),  // c7-c5 (Sicilian)
        BookEntry(Move(Position(6, 4), Position(5, 4)), 60),  // e7-e6 (French)
        BookEntry(Move(Position(6, 2), Position(5, 2)), 50),  // c7-c6 (Caro-Kann)
        BookEntry(Move(Position(6, 3), Position(4, 3)), 40),  // d7-d5 (Scandinavian)
    };

    bookMoves_[afterE4Hash] = blackResponses;

    // Add some responses to d2-d4
    uint64_t afterD4Hash = 0x123456789ABCDEF2ULL; // Hash after 1.d4
    std::vector<BookEntry> blackResponsesD4 = {
        BookEntry(Move(Position(6, 3), Position(4, 3)), 100), // d7-d5 (Queen's Gambit)
        BookEntry(Move(Position(7, 6), Position(5, 5)), 80),  // Ng8-f6 (Indian Defenses)
        BookEntry(Move(Position(6, 5), Position(4, 5)), 60),  // f7-f5 (Dutch)
        BookEntry(Move(Position(6, 4), Position(5, 4)), 50),  // e7-e6 (Queen's Pawn Game)
    };

    bookMoves_[afterD4Hash] = blackResponsesD4;

    isLoaded_ = true;
    // Debug output removed for UCI compatibility
    // std::cout << "Opening book loaded with " << bookMoves_.size() << " positions.\n";
}

uint64_t OpeningBook::generatePositionHash(const ChessGame &game) const
{
    // Simplified hash based on move count and current player
    // In production, use proper Zobrist hashing
    const auto &moveHistory = game.getMoveHistory();
    uint64_t hash = 0x123456789ABCDEF0ULL; // Starting position

    if (moveHistory.empty())
    {
        return hash; // Starting position
    }

    // Simple hash modification based on moves played
    if (moveHistory.size() == 1)
    {
        // After White's first move
        const Move &firstMove = moveHistory[0];
        if (firstMove.getFrom() == Position(1, 4) && firstMove.getTo() == Position(3, 4))
        {
            return 0x123456789ABCDEF1ULL; // After e2-e4
        }
        else if (firstMove.getFrom() == Position(1, 3) && firstMove.getTo() == Position(3, 3))
        {
            return 0x123456789ABCDEF2ULL; // After d2-d4
        }
    }

    // For now, only handle first few moves
    return 0; // Unknown position
}

Move OpeningBook::getBookMove(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return Move(); // Invalid move
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);

    if (it == bookMoves_.end() || it->second.empty())
    {
        return Move(); // No book move
    }

    const std::vector<BookEntry> &moves = it->second;

    // DETERMINISTIC SELECTION: Choose move with highest weight
    // No more random selection for consistent play!

    int bestWeight = -1;
    Move bestMove;

    for (const auto &entry : moves)
    {
        if (entry.weight > bestWeight)
        {
            bestWeight = entry.weight;
            bestMove = entry.move;
        }
    }

    if (bestMove.isValid())
    {
        return bestMove;
    }

    // Fallback to first move
    return moves[0].move;
}

bool OpeningBook::hasBookMove(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return false;
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);
    return it != bookMoves_.end() && !it->second.empty();
}

std::vector<BookEntry> OpeningBook::getBookMoves(const ChessGame &game) const
{
    if (!isLoaded_)
    {
        return {};
    }

    uint64_t hash = generatePositionHash(game);
    auto it = bookMoves_.find(hash);

    if (it != bookMoves_.end())
    {
        return it->second;
    }

    return {};
}

bool OpeningBook::loadFromFile(const std::string &filename)
{
    // Future implementation: parse opening book files
    // For now, just use hardcoded openings
    std::cout << "Loading from file not yet implemented. Using hardcoded openings.\n";
    return isLoaded_;
}
